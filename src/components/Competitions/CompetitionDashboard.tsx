import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Timer, Users, DollarSign, TrendingUp, Calendar, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CompetitionDiscovery from './CompetitionDiscovery';
import CompetitionLeaderboard from './CompetitionLeaderboard';
import CompetitionCreator from './CompetitionCreator';
import { useCompetitions, Competition } from '@/hooks/useCompetitions';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';

interface CompetitionDashboardProps {
  onClose?: () => void;
}

const CompetitionDashboard: React.FC<CompetitionDashboardProps> = ({ onClose }) => {
  const { userCompetitions, refreshCompetitions, refreshUserCompetitions } = useCompetitions();
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null);
  const [activeTab, setActiveTab] = useState<'discover' | 'my-competitions' | 'leaderboard'>('discover');

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refreshCompetitions();
      refreshUserCompetitions();
    }, 30000);

    return () => clearInterval(interval);
  }, [refreshCompetitions, refreshUserCompetitions]);

  const getTimeRemaining = (competition: Competition) => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (isBefore(now, start)) {
      return {
        label: 'Starts in',
        time: formatDistanceToNow(start),
        status: 'upcoming' as const
      };
    } else if (isAfter(now, start) && isBefore(now, end)) {
      return {
        label: 'Ends in',
        time: formatDistanceToNow(end),
        status: 'active' as const
      };
    } else {
      return {
        label: 'Ended',
        time: formatDistanceToNow(end) + ' ago',
        status: 'completed' as const
      };
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const handleCompetitionSelect = (competition: Competition) => {
    setSelectedCompetition(competition);
    setActiveTab('leaderboard');
  };

  const handleBackToDiscovery = () => {
    setSelectedCompetition(null);
    setActiveTab('discover');
  };

  const handleCompetitionCreated = () => {
    refreshCompetitions();
    refreshUserCompetitions();
  };

  if (selectedCompetition) {
    const timeInfo = getTimeRemaining(selectedCompetition);
    
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="h-full bg-[#0A0A0A] text-white overflow-hidden"
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex-shrink-0 p-6 border-b border-white/[0.08]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToDiscovery}
                  className="text-white/60 hover:text-white"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                    <Trophy className="w-6 h-6 text-yellow-400" />
                    {selectedCompetition.name}
                  </h1>
                  <p className="text-white/60 mt-1">
                    {selectedCompetition.description || 'No description available'}
                  </p>
                </div>
              </div>
              {onClose && (
                <Button variant="ghost" onClick={onClose} className="text-white/60 hover:text-white">
                  ✕
                </Button>
              )}
            </div>

            {/* Competition Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Timer className="w-4 h-4 text-blue-400" />
                    <div>
                      <p className="text-white/60 text-sm">{timeInfo.label}</p>
                      <p className="text-white font-semibold">{timeInfo.time}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-400" />
                    <div>
                      <p className="text-white/60 text-sm">Starting Balance</p>
                      <p className="text-white font-semibold">
                        ${selectedCompetition.starting_balance.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-400" />
                    <div>
                      <p className="text-white/60 text-sm">Participants</p>
                      <p className="text-white font-semibold">
                        {selectedCompetition.participant_count || 0}
                        {selectedCompetition.max_participants && ` / ${selectedCompetition.max_participants}`}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(timeInfo.status)}>
                      {selectedCompetition.status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Leaderboard */}
          <div className="flex-1 p-6 overflow-y-auto">
            <CompetitionLeaderboard competitionId={selectedCompetition.id} />
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="h-full bg-[#0A0A0A] text-white overflow-hidden"
    >
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex-shrink-0 p-6 border-b border-white/[0.08]">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white flex items-center gap-2">
                <Trophy className="w-8 h-8 text-yellow-400" />
                Trading Competitions
              </h1>
              <p className="text-white/60 mt-2">
                Compete with traders worldwide in paper trading competitions
              </p>
            </div>
            {onClose && (
              <Button variant="ghost" onClick={onClose} className="text-white/60 hover:text-white">
                ✕
              </Button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="h-full flex flex-col">
            <div className="flex-shrink-0 px-6 pt-6">
              <TabsList className="grid w-full grid-cols-2 bg-[#141414] border border-white/10 shadow-lg">
                <TabsTrigger
                  value="discover"
                  className="data-[state=active]:bg-green-600 data-[state=active]:shadow-lg data-[state=active]:shadow-green-600/20 data-[state=active]:border-green-500/30 font-bold transition-all duration-200"
                >
                  Discover Competitions
                </TabsTrigger>
                <TabsTrigger
                  value="my-competitions"
                  className="data-[state=active]:bg-green-600 data-[state=active]:shadow-lg data-[state=active]:shadow-green-600/20 data-[state=active]:border-green-500/30 font-bold transition-all duration-200"
                >
                  My Competitions ({userCompetitions.length})
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1 overflow-y-auto">
              <TabsContent value="discover" className="h-full p-6 mt-0">
                <CompetitionDiscovery onCompetitionSelect={handleCompetitionSelect} />
              </TabsContent>

              <TabsContent value="my-competitions" className="h-full p-6 mt-0">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-4">My Competitions</h2>
                    {userCompetitions.length === 0 ? (
                      <div className="text-center py-12">
                        <Trophy className="w-16 h-16 text-white/20 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-white/60 mb-2">
                          No competitions joined yet
                        </h3>
                        <p className="text-white/40 mb-4">
                          Join a competition to start trading and competing!
                        </p>
                        <Button 
                          onClick={() => setActiveTab('discover')}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          Discover Competitions
                        </Button>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {userCompetitions.map((userComp, index) => {
                          const competition = userComp.competition;
                          const timeInfo = getTimeRemaining(competition);
                          
                          return (
                            <motion.div
                              key={userComp.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                            >
                              <Card 
                                className="bg-white/[0.02] border-white/[0.08] hover:border-white/[0.16] transition-all duration-200 cursor-pointer"
                                onClick={() => handleCompetitionSelect(competition)}
                              >
                                <CardHeader className="pb-3">
                                  <div className="flex items-start justify-between">
                                    <CardTitle className="text-white text-lg">
                                      {competition.name}
                                    </CardTitle>
                                    <Badge className={getStatusColor(timeInfo.status)}>
                                      {competition.status}
                                    </Badge>
                                  </div>
                                </CardHeader>
                                <CardContent>
                                  <div className="space-y-3">
                                    <div className="flex items-center gap-2 text-sm">
                                      <Timer className="w-4 h-4 text-blue-400" />
                                      <span className="text-white/80">
                                        {timeInfo.label}: {timeInfo.time}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm">
                                      <Calendar className="w-4 h-4 text-purple-400" />
                                      <span className="text-white/80">
                                        Joined {formatDistanceToNow(new Date(userComp.joined_at))} ago
                                      </span>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            </motion.div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>

      {/* Competition Creator (localhost only) */}
      <CompetitionCreator onCompetitionCreated={handleCompetitionCreated} />
    </motion.div>
  );
};

export default CompetitionDashboard;
