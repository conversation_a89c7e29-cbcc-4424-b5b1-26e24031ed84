import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Timer, Users, DollarSign, TrendingUp, Calendar, ArrowLeft, TrendingDown, Search, Filter, SortDesc, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import CompetitionDiscovery from './CompetitionDiscovery';
import CompetitionLeaderboard from './CompetitionLeaderboard';
import CompetitionCreator from './CompetitionCreator';
import { useCompetitions, Competition, LeaderboardEntry } from '@/hooks/useCompetitions';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';

interface CompetitionDashboardProps {
  onClose?: () => void;
}

// Clean Leaderboard Component
const CleanLeaderboard: React.FC<{ competitionId: string }> = ({ competitionId }) => {
  const { getCompetitionDetails } = useCompetitions();
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        const details = await getCompetitionDetails(competitionId);
        if (details) {
          setLeaderboard(details.leaderboard || []);
        }
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
    const interval = setInterval(fetchLeaderboard, 30000);
    return () => clearInterval(interval);
  }, [competitionId, getCompetitionDetails]);

  const formatName = (username: string) => {
    const parts = username.split(' ');
    if (parts.length >= 2) {
      return `${parts[0]} ${parts[1].charAt(0)}.`;
    }
    return username;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  const getRankGlow = (rank: number) => {
    switch (rank) {
      case 1: return 'shadow-lg shadow-yellow-400/20 bg-gradient-to-r from-yellow-400/5 to-transparent';
      case 2: return 'shadow-lg shadow-gray-300/15 bg-gradient-to-r from-gray-300/5 to-transparent';
      case 3: return 'shadow-lg shadow-amber-600/15 bg-gradient-to-r from-amber-600/5 to-transparent';
      default: return '';
    }
  };

  const getRankNumberStyle = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-gradient-to-br from-yellow-400 to-yellow-600 text-black shadow-lg shadow-yellow-400/30';
      case 2: return 'bg-gradient-to-br from-gray-300 to-gray-500 text-black shadow-lg shadow-gray-300/30';
      case 3: return 'bg-gradient-to-br from-amber-600 to-amber-800 text-white shadow-lg shadow-amber-600/30';
      default: return 'bg-[#1a1a1a] border border-white/20 text-white/80';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <Card className="bg-[#141414] border-white/[0.08] shadow-2xl overflow-hidden">
      <CardHeader className="pb-6 bg-gradient-to-r from-[#141414] to-[#1a1a1a]">
        <CardTitle className="text-white flex items-center gap-3 text-2xl">
          <Trophy className="w-7 h-7 text-yellow-400 drop-shadow-lg" />
          <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
            Leaderboard
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-0">
          {leaderboard.map((entry, index) => (
            <motion.div
              key={entry.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05, type: "spring", stiffness: 100 }}
              className={`flex items-center justify-between p-6 hover:bg-white/[0.02] transition-all duration-300 border-b border-white/[0.05] last:border-b-0 ${getRankGlow(entry.current_rank)}`}
            >
              {/* Left side - Rank and User */}
              <div className="flex items-center gap-6">
                {/* Rank Number */}
                <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getRankNumberStyle(entry.current_rank)}`}>
                  {entry.current_rank}
                </div>

                {/* User Info */}
                <div>
                  <h4 className="text-white font-bold text-lg">
                    {formatName(entry.username)}
                  </h4>
                </div>
              </div>

              {/* Right side - Performance */}
              <div className="text-right">
                <div className="text-white font-bold text-xl mb-1">
                  {formatCurrency(entry.portfolio_value)}
                </div>
                <div className={`font-bold text-lg ${
                  entry.return_percent >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {formatPercentage(entry.return_percent)}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const CompetitionDashboard: React.FC<CompetitionDashboardProps> = ({ onClose }) => {
  const { userCompetitions, refreshCompetitions, refreshUserCompetitions, competitions } = useCompetitions();
  const [selectedCompetition, setSelectedCompetition] = useState<Competition | null>(null);
  const [activeTab, setActiveTab] = useState<'discover' | 'my-competitions' | 'leaderboard'>('discover');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'open' | 'active' | 'completed'>('all');
  const [sortBy, setSortBy] = useState<'newest' | 'participants' | 'prize'>('newest');

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refreshCompetitions();
      refreshUserCompetitions();
    }, 30000);

    return () => clearInterval(interval);
  }, [refreshCompetitions, refreshUserCompetitions]);

  const getTimeRemaining = (competition: Competition) => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (isBefore(now, start)) {
      return {
        label: 'Starts in',
        time: formatDistanceToNow(start),
        status: 'upcoming' as const
      };
    } else if (isAfter(now, start) && isBefore(now, end)) {
      return {
        label: 'Ends in',
        time: formatDistanceToNow(end),
        status: 'active' as const
      };
    } else {
      return {
        label: 'Ended',
        time: formatDistanceToNow(end) + ' ago',
        status: 'completed' as const
      };
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30';
      case 'active': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const handleCompetitionSelect = (competition: Competition) => {
    setSelectedCompetition(competition);
    setActiveTab('leaderboard');
  };

  const handleBackToDiscovery = () => {
    setSelectedCompetition(null);
    setActiveTab('discover');
  };

  const handleCompetitionCreated = () => {
    refreshCompetitions();
    refreshUserCompetitions();
  };

  if (selectedCompetition) {
    const timeInfo = getTimeRemaining(selectedCompetition);
    
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        className="h-full bg-[#0A0A0A] text-white overflow-hidden"
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="flex-shrink-0 p-6 border-b border-white/[0.08]">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBackToDiscovery}
                  className="text-white/60 hover:text-white"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
                <div>
                  <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                    <Trophy className="w-6 h-6 text-yellow-400" />
                    {selectedCompetition.name}
                  </h1>
                  <p className="text-white/60 mt-1">
                    {selectedCompetition.description || 'No description available'}
                  </p>
                </div>
              </div>
              {onClose && (
                <Button variant="ghost" onClick={onClose} className="text-white/60 hover:text-white">
                  ✕
                </Button>
              )}
            </div>

            {/* Competition Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Timer className="w-4 h-4 text-blue-400" />
                    <div>
                      <p className="text-white/60 text-sm">{timeInfo.label}</p>
                      <p className="text-white font-semibold">{timeInfo.time}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-400" />
                    <div>
                      <p className="text-white/60 text-sm">Starting Balance</p>
                      <p className="text-white font-semibold">
                        ${selectedCompetition.starting_balance.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-purple-400" />
                    <div>
                      <p className="text-white/60 text-sm">Participants</p>
                      <p className="text-white font-semibold">
                        {selectedCompetition.participant_count || 0}
                        {selectedCompetition.max_participants && ` / ${selectedCompetition.max_participants}`}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white/[0.02] border-white/[0.08]">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(timeInfo.status)}>
                      {selectedCompetition.status}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Clean Leaderboard */}
          <div className="flex-1 p-6 overflow-y-auto">
            <CleanLeaderboard competitionId={selectedCompetition.id} />
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="h-full bg-[#0A0A0A] text-white overflow-hidden"
    >
      <div className="h-full flex flex-col">
        {/* Clean Advanced Header */}
        <div className="flex-shrink-0 p-8 border-b border-white/[0.08] bg-gradient-to-r from-[#141414] to-[#1a1a1a]">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-white flex items-center gap-4">
                <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-green-400 to-green-600 flex items-center justify-center shadow-lg shadow-green-400/20">
                  <Target className="w-6 h-6 text-white" />
                </div>
                <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                  Trading Competitions
                </span>
              </h1>
              <p className="text-white/60 mt-3 text-xl font-medium ml-16">
                Compete with traders worldwide in paper trading competitions
              </p>
            </div>
            {onClose && (
              <Button variant="ghost" onClick={onClose} className="text-white/60 hover:text-white text-2xl p-3">
                ✕
              </Button>
            )}
          </div>

          {/* Advanced Search Bar */}
          <div className="mt-8 flex items-center gap-6">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Search className="h-6 w-6 text-white/40" />
              </div>
              <input
                type="text"
                placeholder="Search competitions by name, description, or scope..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-[#1a1a1a] border border-white/20 rounded-xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-green-400/50 focus:border-green-400/50 shadow-lg text-lg font-medium"
              />
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-400/10 to-transparent opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </div>

            {/* Filter Controls */}
            <div className="flex items-center gap-3">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className="px-4 py-4 bg-[#1a1a1a] border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-green-400/50 font-medium"
              >
                <option value="all">All Status</option>
                <option value="open">Open</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
              </select>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-4 py-4 bg-[#1a1a1a] border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-green-400/50 font-medium"
              >
                <option value="newest">Newest</option>
                <option value="participants">Most Participants</option>
                <option value="prize">Highest Prize</option>
              </select>
            </div>
          </div>
        </div>

        {/* Clean Navigation */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full flex flex-col">
            <div className="flex-shrink-0 px-8 pt-6">
              <div className="flex items-center gap-4">
                <Button
                  variant={activeTab === 'discover' ? 'default' : 'outline'}
                  onClick={() => setActiveTab('discover')}
                  className={`px-6 py-3 font-bold text-lg transition-all duration-200 ${
                    activeTab === 'discover'
                      ? 'bg-green-600 hover:bg-green-700 shadow-lg shadow-green-600/20 border-green-500/30'
                      : 'bg-[#141414] border-white/20 text-white/80 hover:bg-white/5 hover:border-white/30'
                  }`}
                >
                  <Search className="w-5 h-5 mr-2" />
                  Discover Competitions
                </Button>
                <Button
                  variant={activeTab === 'my-competitions' ? 'default' : 'outline'}
                  onClick={() => setActiveTab('my-competitions')}
                  className={`px-6 py-3 font-bold text-lg transition-all duration-200 ${
                    activeTab === 'my-competitions'
                      ? 'bg-green-600 hover:bg-green-700 shadow-lg shadow-green-600/20 border-green-500/30'
                      : 'bg-[#141414] border-white/20 text-white/80 hover:bg-white/5 hover:border-white/30'
                  }`}
                >
                  <Trophy className="w-5 h-5 mr-2" />
                  My Competitions ({userCompetitions.length})
                </Button>
              </div>
            </div>

            <div className="flex-1 overflow-hidden">
              {activeTab === 'discover' && (
                <div className="h-full overflow-y-auto p-8">
                  <CompetitionDiscovery onCompetitionSelect={handleCompetitionSelect} />
                </div>
              )}

              {activeTab === 'my-competitions' && (
                <div className="h-full overflow-y-auto p-8">
                <div className="space-y-6">
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-4">My Competitions</h2>
                    {userCompetitions.length === 0 ? (
                      <div className="text-center py-12">
                        <Trophy className="w-16 h-16 text-white/20 mx-auto mb-4" />
                        <h3 className="text-xl font-semibold text-white/60 mb-2">
                          No competitions joined yet
                        </h3>
                        <p className="text-white/40 mb-4">
                          Join a competition to start trading and competing!
                        </p>
                        <Button 
                          onClick={() => setActiveTab('discover')}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          Discover Competitions
                        </Button>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {userCompetitions.map((userComp, index) => {
                          const competition = userComp.competition;
                          const timeInfo = getTimeRemaining(competition);
                          
                          return (
                            <motion.div
                              key={userComp.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                            >
                              <Card 
                                className="bg-white/[0.02] border-white/[0.08] hover:border-white/[0.16] transition-all duration-200 cursor-pointer"
                                onClick={() => handleCompetitionSelect(competition)}
                              >
                                <CardHeader className="pb-3">
                                  <div className="flex items-start justify-between">
                                    <CardTitle className="text-white text-lg">
                                      {competition.name}
                                    </CardTitle>
                                    <Badge className={getStatusColor(timeInfo.status)}>
                                      {competition.status}
                                    </Badge>
                                  </div>
                                </CardHeader>
                                <CardContent>
                                  <div className="space-y-3">
                                    <div className="flex items-center gap-2 text-sm">
                                      <Timer className="w-4 h-4 text-blue-400" />
                                      <span className="text-white/80">
                                        {timeInfo.label}: {timeInfo.time}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-2 text-sm">
                                      <Calendar className="w-4 h-4 text-purple-400" />
                                      <span className="text-white/80">
                                        Joined {formatDistanceToNow(new Date(userComp.joined_at))} ago
                                      </span>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            </motion.div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>

      {/* Competition Creator (localhost only) */}
      <CompetitionCreator onCompetitionCreated={handleCompetitionCreated} />
    </motion.div>
  );
};

export default CompetitionDashboard;
