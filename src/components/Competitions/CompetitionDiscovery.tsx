import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Users, Clock, DollarSign, Calendar, Target, Award, Timer, Globe, Crown, TrendingUp, TrendingDown, Search, Filter, SortDesc } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCompetitions, Competition, LeaderboardEntry } from '@/hooks/useCompetitions';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';
import CompetitionCountdown from './CompetitionCountdown';
import { OFFICIAL_OSIS_BUSINESS_ID, OFFICIAL_OSIS_HANDLE } from '@/types/whopCompetition';

interface CompetitionDiscoveryProps {
  onCompetitionSelect?: (competition: Competition) => void;
}

// Clean Leaderboard Component for Discovery
const CleanLeaderboard: React.FC<{ competitionId: string }> = ({ competitionId }) => {
  const { getCompetitionDetails } = useCompetitions();
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        const details = await getCompetitionDetails(competitionId);
        if (details) {
          setLeaderboard(details.leaderboard || []);
        }
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
    const interval = setInterval(fetchLeaderboard, 30000);
    return () => clearInterval(interval);
  }, [competitionId, getCompetitionDetails]);

  const formatName = (username: string) => {
    const parts = username.split(' ');
    if (parts.length >= 2) {
      return `${parts[0]} ${parts[1].charAt(0)}.`;
    }
    return username;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  const getRankGlow = (rank: number) => {
    switch (rank) {
      case 1: return 'shadow-lg shadow-yellow-400/20 bg-gradient-to-r from-yellow-400/5 to-transparent';
      case 2: return 'shadow-lg shadow-gray-300/15 bg-gradient-to-r from-gray-300/5 to-transparent';
      case 3: return 'shadow-lg shadow-amber-600/15 bg-gradient-to-r from-amber-600/5 to-transparent';
      default: return '';
    }
  };

  const getRankNumberStyle = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-gradient-to-br from-yellow-400 to-yellow-600 text-black shadow-lg shadow-yellow-400/30';
      case 2: return 'bg-gradient-to-br from-gray-300 to-gray-500 text-black shadow-lg shadow-gray-300/30';
      case 3: return 'bg-gradient-to-br from-amber-600 to-amber-800 text-white shadow-lg shadow-amber-600/30';
      default: return 'bg-[#1a1a1a] border border-white/20 text-white/80';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <Card className="bg-[#141414] border-white/[0.08] shadow-2xl overflow-hidden">
      <CardHeader className="pb-6 bg-gradient-to-r from-[#141414] to-[#1a1a1a]">
        <CardTitle className="text-white flex items-center gap-3 text-2xl">
          <Trophy className="w-7 h-7 text-yellow-400 drop-shadow-lg" />
          <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
            Leaderboard
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-0">
          {leaderboard.slice(0, 10).map((entry, index) => (
            <motion.div
              key={entry.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05, type: "spring", stiffness: 100 }}
              className={`flex items-center justify-between p-6 hover:bg-white/[0.02] transition-all duration-300 border-b border-white/[0.05] last:border-b-0 ${getRankGlow(entry.current_rank)}`}
            >
              {/* Left side - Rank and User */}
              <div className="flex items-center gap-6">
                {/* Rank Number */}
                <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getRankNumberStyle(entry.current_rank)}`}>
                  {entry.current_rank}
                </div>

                {/* User Info */}
                <div>
                  <h4 className="text-white font-bold text-lg">
                    {formatName(entry.username)}
                  </h4>
                </div>
              </div>

              {/* Right side - Performance */}
              <div className="text-right">
                <div className="text-white font-bold text-xl mb-1">
                  {formatCurrency(entry.portfolio_value)}
                </div>
                <div className={`font-bold text-lg ${
                  entry.return_percent >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {formatPercentage(entry.return_percent)}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const CompetitionDiscovery: React.FC<CompetitionDiscoveryProps> = ({
  onCompetitionSelect
}) => {
  const { competitions, userCompetitions, loading, joinCompetition } = useCompetitions();
  const { toast } = useToast();
  const [filter, setFilter] = useState<'all' | 'open' | 'active' | 'completed'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'participants' | 'prize'>('newest');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'active': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getCompetitionScopeBadge = (competition: Competition) => {
    if (!competition.competition_scope || competition.competition_scope === 'public') {
      return (
        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
          <Globe className="w-3 h-3 mr-1" />
          Public
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_local') {
      return (
        <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
          <Users className="w-3 h-3 mr-1" />
          Community
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_cross_community') {
      const isOfficialOsis = competition.whop_business_id === OFFICIAL_OSIS_BUSINESS_ID ||
                            competition.whop_business_handle === OFFICIAL_OSIS_HANDLE;
      return (
        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
          <Crown className="w-3 h-3 mr-1" />
          {isOfficialOsis ? 'Official Osis' : 'Cross-Community'}
        </Badge>
      );
    }

    return null;
  };

  const getTimeRemaining = (competition: Competition) => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (isBefore(now, start)) {
      return {
        label: 'Starts in',
        time: formatDistanceToNow(start),
        urgent: false
      };
    } else if (isAfter(now, start) && isBefore(now, end)) {
      const remaining = formatDistanceToNow(end);
      return {
        label: 'Ends in',
        time: remaining,
        urgent: remaining.includes('hour') || remaining.includes('minute')
      };
    } else {
      return {
        label: 'Ended',
        time: formatDistanceToNow(end) + ' ago',
        urgent: false
      };
    }
  };

  const isUserParticipating = (competitionId: string) => {
    return userCompetitions.some(uc => uc.competition_id === competitionId);
  };

  const handleJoinCompetition = async (competitionId: string) => {
    try {
      await joinCompetition(competitionId);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  const filteredCompetitions = competitions.filter(competition => {
    // Status filter
    if (filter !== 'all' && competition.status !== filter) return false;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        competition.name.toLowerCase().includes(query) ||
        competition.description?.toLowerCase().includes(query) ||
        competition.scope?.toLowerCase().includes(query)
      );
    }

    return true;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'participants':
        return (b.participant_count || 0) - (a.participant_count || 0);
      case 'prize':
        return (b.starting_balance || 0) - (a.starting_balance || 0);
      case 'newest':
      default:
        return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
    }
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white overflow-hidden flex flex-col items-center">
      {/* Logo */}
      <div className="pt-16 pb-8">
        <img
          src="http://thecodingkid.oyosite.com/logo_only.png"
          alt="OSIS Logo"
          className="w-12 h-12 mx-auto"
        />
      </div>

      {/* Title */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-light text-white tracking-tight -mt-2">
          Discover Trading Competitions
        </h1>
      </div>

      {/* Search Bar */}
      <div className="w-full max-w-md mb-16">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <img
              src="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/icons//Tradingview%20Financial%20Chart.png"
              alt="Search"
              className="w-4 h-4 opacity-60"
            />
          </div>
          <input
            type="text"
            placeholder="Search for anything..."
            className="w-full pl-12 pr-4 py-3 bg-[#2A2A2A] border border-white/20 rounded-full text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/40 shadow-lg"
            style={{
              boxShadow: '0 0 20px rgba(255, 255, 255, 0.1), inset 0 1px 3px rgba(255, 255, 255, 0.1)'
            }}
          />
        </div>
      </div>

      {/* Competition Grid */}
      <div className="flex-1 w-full max-w-4xl px-8">
        <div className="grid grid-cols-2 gap-6 mb-8">
          {/* Competition Box 1 */}
          <div className="bg-[#2A2A2A] rounded-lg h-32 border border-white/10 relative overflow-hidden">
            <div className="absolute top-3 left-3 text-white/60 text-sm">
              $10,000 Futures
            </div>
            <div className="absolute bottom-3 left-3 text-white/40 text-xs">
              Hosted by: Cale L.
            </div>
          </div>

          {/* Competition Box 2 */}
          <div className="bg-[#2A2A2A] rounded-lg h-32 border border-white/10"></div>

          {/* Competition Box 3 */}
          <div className="bg-[#2A2A2A] rounded-lg h-32 border border-white/10"></div>

          {/* Competition Box 4 */}
          <div className="bg-[#2A2A2A] rounded-lg h-32 border border-white/10"></div>
        </div>

        {/* Show More */}
        <div className="text-center pb-8">
          <button className="text-white/60 text-sm hover:text-white/80 transition-colors">
            Show more
          </button>
        </div>
      </div>
    </div>
  );
};

export default CompetitionDiscovery;
