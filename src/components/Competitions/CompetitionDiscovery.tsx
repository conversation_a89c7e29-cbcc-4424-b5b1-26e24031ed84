import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Trophy, Users, Clock, DollarSign, Calendar, Target, Award, Timer, Globe, Crown, TrendingUp, TrendingDown, Search, Filter, SortDesc } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useCompetitions, Competition, LeaderboardEntry } from '@/hooks/useCompetitions';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow, isAfter, isBefore } from 'date-fns';
import CompetitionCountdown from './CompetitionCountdown';
import { OFFICIAL_OSIS_BUSINESS_ID, OFFICIAL_OSIS_HANDLE } from '@/types/whopCompetition';

interface CompetitionDiscoveryProps {
  onCompetitionSelect?: (competition: Competition) => void;
}

// Clean Leaderboard Component for Discovery
const CleanLeaderboard: React.FC<{ competitionId: string }> = ({ competitionId }) => {
  const { getCompetitionDetails } = useCompetitions();
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLeaderboard = async () => {
      try {
        const details = await getCompetitionDetails(competitionId);
        if (details) {
          setLeaderboard(details.leaderboard || []);
        }
      } catch (error) {
        console.error('Error fetching leaderboard:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLeaderboard();
    const interval = setInterval(fetchLeaderboard, 30000);
    return () => clearInterval(interval);
  }, [competitionId, getCompetitionDetails]);

  const formatName = (username: string) => {
    const parts = username.split(' ');
    if (parts.length >= 2) {
      return `${parts[0]} ${parts[1].charAt(0)}.`;
    }
    return username;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percent: number) => {
    const sign = percent >= 0 ? '+' : '';
    return `${sign}${percent.toFixed(2)}%`;
  };

  const getRankGlow = (rank: number) => {
    switch (rank) {
      case 1: return 'shadow-lg shadow-yellow-400/20 bg-gradient-to-r from-yellow-400/5 to-transparent';
      case 2: return 'shadow-lg shadow-gray-300/15 bg-gradient-to-r from-gray-300/5 to-transparent';
      case 3: return 'shadow-lg shadow-amber-600/15 bg-gradient-to-r from-amber-600/5 to-transparent';
      default: return '';
    }
  };

  const getRankNumberStyle = (rank: number) => {
    switch (rank) {
      case 1: return 'bg-gradient-to-br from-yellow-400 to-yellow-600 text-black shadow-lg shadow-yellow-400/30';
      case 2: return 'bg-gradient-to-br from-gray-300 to-gray-500 text-black shadow-lg shadow-gray-300/30';
      case 3: return 'bg-gradient-to-br from-amber-600 to-amber-800 text-white shadow-lg shadow-amber-600/30';
      default: return 'bg-[#1a1a1a] border border-white/20 text-white/80';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <Card className="bg-[#141414] border-white/[0.08] shadow-2xl overflow-hidden">
      <CardHeader className="pb-6 bg-gradient-to-r from-[#141414] to-[#1a1a1a]">
        <CardTitle className="text-white flex items-center gap-3 text-2xl">
          <Trophy className="w-7 h-7 text-yellow-400 drop-shadow-lg" />
          <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
            Leaderboard
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <div className="space-y-0">
          {leaderboard.slice(0, 10).map((entry, index) => (
            <motion.div
              key={entry.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.05, type: "spring", stiffness: 100 }}
              className={`flex items-center justify-between p-6 hover:bg-white/[0.02] transition-all duration-300 border-b border-white/[0.05] last:border-b-0 ${getRankGlow(entry.current_rank)}`}
            >
              {/* Left side - Rank and User */}
              <div className="flex items-center gap-6">
                {/* Rank Number */}
                <div className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg ${getRankNumberStyle(entry.current_rank)}`}>
                  {entry.current_rank}
                </div>

                {/* User Info */}
                <div>
                  <h4 className="text-white font-bold text-lg">
                    {formatName(entry.username)}
                  </h4>
                </div>
              </div>

              {/* Right side - Performance */}
              <div className="text-right">
                <div className="text-white font-bold text-xl mb-1">
                  {formatCurrency(entry.portfolio_value)}
                </div>
                <div className={`font-bold text-lg ${
                  entry.return_percent >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  {formatPercentage(entry.return_percent)}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

const CompetitionDiscovery: React.FC<CompetitionDiscoveryProps> = ({
  onCompetitionSelect
}) => {
  const { competitions, userCompetitions, loading, joinCompetition } = useCompetitions();
  const { toast } = useToast();
  const [filter, setFilter] = useState<'all' | 'open' | 'active' | 'completed'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'participants' | 'prize'>('newest');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'active': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getCompetitionScopeBadge = (competition: Competition) => {
    if (!competition.competition_scope || competition.competition_scope === 'public') {
      return (
        <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
          <Globe className="w-3 h-3 mr-1" />
          Public
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_local') {
      return (
        <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
          <Users className="w-3 h-3 mr-1" />
          Community
        </Badge>
      );
    }

    if (competition.competition_scope === 'whop_cross_community') {
      const isOfficialOsis = competition.whop_business_id === OFFICIAL_OSIS_BUSINESS_ID ||
                            competition.whop_business_handle === OFFICIAL_OSIS_HANDLE;
      return (
        <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/30">
          <Crown className="w-3 h-3 mr-1" />
          {isOfficialOsis ? 'Official Osis' : 'Cross-Community'}
        </Badge>
      );
    }

    return null;
  };

  const getTimeRemaining = (competition: Competition) => {
    const now = new Date();
    const start = new Date(competition.competition_start);
    const end = new Date(competition.competition_end);

    if (isBefore(now, start)) {
      return {
        label: 'Starts in',
        time: formatDistanceToNow(start),
        urgent: false
      };
    } else if (isAfter(now, start) && isBefore(now, end)) {
      const remaining = formatDistanceToNow(end);
      return {
        label: 'Ends in',
        time: remaining,
        urgent: remaining.includes('hour') || remaining.includes('minute')
      };
    } else {
      return {
        label: 'Ended',
        time: formatDistanceToNow(end) + ' ago',
        urgent: false
      };
    }
  };

  const isUserParticipating = (competitionId: string) => {
    return userCompetitions.some(uc => uc.competition_id === competitionId);
  };

  const handleJoinCompetition = async (competitionId: string) => {
    try {
      await joinCompetition(competitionId);
    } catch (error) {
      // Error is already handled in the hook
    }
  };

  const filteredCompetitions = competitions.filter(competition => {
    // Status filter
    if (filter !== 'all' && competition.status !== filter) return false;

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        competition.name.toLowerCase().includes(query) ||
        competition.description?.toLowerCase().includes(query) ||
        competition.scope?.toLowerCase().includes(query)
      );
    }

    return true;
  }).sort((a, b) => {
    switch (sortBy) {
      case 'participants':
        return (b.participant_count || 0) - (a.participant_count || 0);
      case 'prize':
        return (b.starting_balance || 0) - (a.starting_balance || 0);
      case 'newest':
      default:
        return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
    }
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white flex items-center gap-2">
            <Trophy className="w-6 h-6 text-yellow-400" />
            Trading Competitions
          </h2>
          <p className="text-white/60 mt-1">
            Join paper trading competitions and compete with traders worldwide
          </p>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex gap-2">
        {['all', 'open', 'active', 'completed'].map((status) => (
          <Button
            key={status}
            variant={filter === status ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter(status as any)}
            className={filter === status ? 'bg-green-600 hover:bg-green-700' : ''}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Button>
        ))}
      </div>

      {/* Competitions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCompetitions.map((competition, index) => {
          const timeInfo = getTimeRemaining(competition);
          const isParticipating = isUserParticipating(competition.id);

          return (
            <motion.div
              key={competition.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="bg-white/[0.02] border-white/[0.08] hover:border-white/[0.16] transition-all duration-200 cursor-pointer group"
                    onClick={() => onCompetitionSelect?.(competition)}>
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <CardTitle className="text-white text-lg group-hover:text-green-400 transition-colors">
                          {competition.name}
                        </CardTitle>
                        {getCompetitionScopeBadge(competition)}
                      </div>
                      <CardDescription className="text-white/60 mt-1 line-clamp-2">
                        {competition.description || 'No description available'}
                      </CardDescription>
                    </div>
                    <Badge className={getStatusColor(competition.status)}>
                      {competition.status}
                    </Badge>
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Key Stats */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center gap-2 text-sm">
                      <DollarSign className="w-4 h-4 text-green-400" />
                      <span className="text-white/80">
                        ${competition.starting_balance.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="w-4 h-4 text-blue-400" />
                      <span className="text-white/80">
                        {competition.participant_count || 0}
                        {competition.max_participants && ` / ${competition.max_participants}`}
                      </span>
                    </div>
                  </div>

                  {/* Competition Countdown */}
                  <CompetitionCountdown
                    competitionStart={competition.competition_start}
                    competitionEnd={competition.competition_end}
                    registrationStart={competition.registration_start}
                    registrationEnd={competition.registration_end}
                    className="mb-2"
                  />

                  {/* Prize Pool */}
                  {competition.prize_pool && competition.prize_pool > 0 && (
                    <div className="flex items-center gap-2 text-sm">
                      <Award className="w-4 h-4 text-yellow-400" />
                      <span className="text-white/80">
                        Prize: ${competition.prize_pool.toLocaleString()}
                      </span>
                    </div>
                  )}

                  {/* Action Button */}
                  <div className="pt-2">
                    {isParticipating ? (
                      <Button 
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          onCompetitionSelect?.(competition);
                        }}
                      >
                        <Trophy className="w-4 h-4 mr-2" />
                        View Competition
                      </Button>
                    ) : competition.status === 'open' ? (
                      <Button 
                        className="w-full bg-green-600 hover:bg-green-700"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleJoinCompetition(competition.id);
                        }}
                      >
                        <Target className="w-4 h-4 mr-2" />
                        Join Competition
                      </Button>
                    ) : (
                      <Button 
                        variant="outline" 
                        className="w-full"
                        onClick={(e) => {
                          e.stopPropagation();
                          onCompetitionSelect?.(competition);
                        }}
                      >
                        <Trophy className="w-4 h-4 mr-2" />
                        View Details
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Empty State */}
      {filteredCompetitions.length === 0 && (
        <div className="text-center py-12">
          <Trophy className="w-16 h-16 text-white/20 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white/60 mb-2">
            No competitions found
          </h3>
          <p className="text-white/40">
            {filter === 'all' 
              ? 'No competitions are currently available'
              : `No ${filter} competitions found`
            }
          </p>
        </div>
      )}
    </div>
  );
};

export default CompetitionDiscovery;
