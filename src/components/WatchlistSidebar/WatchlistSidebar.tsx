import React, { useState } from 'react';
import { useWatchlist } from '@/contexts/WatchlistContext';
import { ChevronLeft, ChevronRight, Star, TrendingUp, TrendingDown, Minus } from 'lucide-react';
import './WatchlistSidebar.css';

interface WatchlistSidebarProps {
  onSymbolSelect?: (symbol: string) => void;
  onToggle?: (isCollapsed: boolean) => void;
}

const WatchlistSidebar: React.FC<WatchlistSidebarProps> = ({ onSymbolSelect, onToggle }) => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { watchlist, isLoading, removeFromWatchlist, refreshPrices } = useWatchlist();

  const handleSymbolClick = (symbol: string) => {
    if (onSymbolSelect) {
      onSymbolSelect(symbol);
    }
  };

  const handleRemoveFromWatchlist = async (e: React.MouseEvent, symbol: string) => {
    e.stopPropagation();
    await removeFromWatchlist(symbol);
  };

  return (
    <div className={`
      fixed left-0 top-0 h-full bg-[#0F0F0F] border-r border-white/[0.08] 
      transition-all duration-300 ease-in-out z-50 backdrop-blur-sm
      ${isCollapsed ? 'w-12' : 'w-80'}
    `}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/[0.08]">
        {!isCollapsed && (
          <div className="flex items-center gap-3">
            <h2 className="text-white font-semibold text-sm">Watchlist</h2>
            <span className="text-xs bg-white/[0.08] text-white/60 px-2 py-0.5 rounded border border-white/[0.10]">
              {watchlist.length}
            </span>
          </div>
        )}
        
        <button
          onClick={() => {
            const newCollapsedState = !isCollapsed;
            setIsCollapsed(newCollapsedState);
            if (onToggle) {
              onToggle(newCollapsedState);
            }
          }}
          className="w-8 h-8 flex items-center justify-center rounded-lg bg-white/[0.05] hover:bg-white/[0.1]
                     border border-white/[0.08] hover:border-white/[0.15] transition-all duration-200"
          title={isCollapsed ? 'Expand watchlist' : 'Collapse watchlist'}
        >
          {isCollapsed ? (
            <ChevronRight className="w-4 h-4 text-white/60" />
          ) : (
            <ChevronLeft className="w-4 h-4 text-white/60" />
          )}
        </button>
      </div>

      {/* Content */}
      {!isCollapsed && (
        <div className="flex flex-col h-full">
          {/* Refresh Button */}
          <div className="p-4 border-b border-white/[0.05]">
            <button
              onClick={refreshPrices}
              className="w-full text-xs bg-white/[0.05] hover:bg-white/[0.08] text-white/70 hover:text-white 
                         px-3 py-2 rounded-lg border border-white/[0.08] hover:border-white/[0.15] 
                         transition-all duration-200 font-medium"
            >
              Refresh Prices
            </button>
          </div>

          {/* Watchlist Items */}
          <div className="flex-1 overflow-y-auto">
            {isLoading ? (
              <div className="p-4 text-center">
                <div className="text-white/40 text-sm">Loading watchlist...</div>
              </div>
            ) : watchlist.length === 0 ? (
              <div className="p-4 text-center">
                <div className="w-12 h-12 bg-white/[0.05] rounded-xl flex items-center justify-center mb-3 border border-white/[0.08] mx-auto">
                  <Star className="w-6 h-6 text-white/40" />
                </div>
                <p className="text-white/50 text-sm mb-2">No stocks in watchlist</p>
                <p className="text-white/30 text-xs">Add stocks from the screener</p>
              </div>
            ) : (
              <div className="space-y-1 p-2">
                {watchlist.map((stock) => {
                  const isPositive = stock.change >= 0;
                  const changeColor = isPositive ? 'text-[#00e7b6]' : 'text-[#ff4757]';
                  
                  return (
                    <div
                      key={stock.symbol}
                      onClick={() => handleSymbolClick(stock.symbol)}
                      className="group p-3 rounded-lg bg-white/[0.02] hover:bg-white/[0.05] 
                                 border border-white/[0.05] hover:border-white/[0.1] 
                                 transition-all duration-200 cursor-pointer"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <span className="font-semibold text-white text-sm truncate">
                              {stock.symbol}
                            </span>
                            <button
                              onClick={(e) => handleRemoveFromWatchlist(e, stock.symbol)}
                              className="opacity-0 group-hover:opacity-100 w-6 h-6 flex items-center justify-center 
                                         rounded bg-white/[0.05] hover:bg-red-500/20 text-white/40 hover:text-red-400 
                                         transition-all duration-200"
                              title="Remove from watchlist"
                            >
                              <Minus className="w-3 h-3" />
                            </button>
                          </div>
                          
                          <div className="flex items-center justify-between">
                            <span className="text-white/90 font-medium text-sm">
                              ${stock.price.toFixed(2)}
                            </span>
                            <div className="flex items-center gap-1">
                              {isPositive ? (
                                <TrendingUp className="w-3 h-3 text-[#00e7b6]" />
                              ) : (
                                <TrendingDown className="w-3 h-3 text-[#ff4757]" />
                              )}
                              <span className={`text-xs font-medium ${changeColor}`}>
                                {isPositive ? '+' : ''}{stock.changePercent.toFixed(2)}%
                              </span>
                            </div>
                          </div>
                          
                          <div className="text-white/50 text-xs mt-1 truncate">
                            {stock.name}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Collapsed State - Show minimal icons */}
      {isCollapsed && (
        <div className="p-2">
          <div className="space-y-2">
            {watchlist.slice(0, 8).map((stock) => {
              const isPositive = stock.change >= 0;
              
              return (
                <div
                  key={stock.symbol}
                  onClick={() => handleSymbolClick(stock.symbol)}
                  className="w-8 h-8 rounded-lg bg-white/[0.02] hover:bg-white/[0.05] 
                             border border-white/[0.05] hover:border-white/[0.1] 
                             flex items-center justify-center cursor-pointer transition-all duration-200
                             relative group"
                  title={`${stock.symbol} - $${stock.price.toFixed(2)} (${isPositive ? '+' : ''}${stock.changePercent.toFixed(2)}%)`}
                >
                  <span className="text-white/70 text-xs font-medium">
                    {stock.symbol.charAt(0)}
                  </span>
                  <div className={`absolute -right-1 -top-1 w-2 h-2 rounded-full ${
                    isPositive ? 'bg-[#00e7b6]' : 'bg-[#ff4757]'
                  }`} />
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default WatchlistSidebar;
