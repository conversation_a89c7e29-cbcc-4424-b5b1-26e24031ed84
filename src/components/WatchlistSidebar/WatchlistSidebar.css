/* WatchlistSidebar Styles */
.watchlist-sidebar {
  /* Custom scrollbar for watchlist */
}

.watchlist-sidebar::-webkit-scrollbar {
  width: 4px;
}

.watchlist-sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 2px;
}

.watchlist-sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
}

.watchlist-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.15);
}

/* Smooth transitions for sidebar */
.watchlist-sidebar-transition {
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Hover effects for watchlist items */
.watchlist-item {
  transition: all 0.2s ease;
}

.watchlist-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Price change animations */
.price-positive {
  color: #00e7b6;
}

.price-negative {
  color: #ff4757;
}

/* Collapsed state animations */
.collapsed-icon {
  transition: all 0.2s ease;
}

.collapsed-icon:hover {
  transform: scale(1.1);
}

/* Custom backdrop blur for better performance */
.watchlist-backdrop {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Loading state */
.watchlist-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .watchlist-sidebar {
    width: 100% !important;
    position: fixed;
    z-index: 100;
  }
  
  .watchlist-sidebar.collapsed {
    width: 0 !important;
    overflow: hidden;
  }
}

/* Focus states for accessibility */
.watchlist-button:focus {
  outline: 2px solid rgba(255, 255, 255, 0.2);
  outline-offset: 2px;
}

/* Smooth color transitions */
.watchlist-text {
  transition: color 0.2s ease;
}

/* Border glow effects */
.watchlist-border-glow {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.08);
  transition: box-shadow 0.2s ease;
}

.watchlist-border-glow:hover {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.15);
}
