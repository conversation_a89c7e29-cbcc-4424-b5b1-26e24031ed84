import React, { useEffect, useRef, useState } from 'react';
import TradingChart from '@/components/TradingChart/TradingChart';
import { useWhopUser } from '@/contexts/WhopContext';
import { Button } from '@/components/ui/button';

const Trading: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { isWhopUser } = useWhopUser();
  const [showDebug, setShowDebug] = useState(false);

  // Show debug panel in localhost
  const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  // Ensure the page takes full viewport height and removes any default margins
  useEffect(() => {
    // Set body styles for full-screen trading experience
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.overflow = 'hidden';
    document.documentElement.style.height = '100%';
    document.body.style.height = '100%';

    // Trigger layout recalculation after mount to ensure proper chart display
    // This mimics the behavior that happens when dropdown menus are activated
    const triggerLayoutRecalculation = () => {
      if (containerRef.current) {
        // Force a reflow by reading layout properties
        const container = containerRef.current;
        container.offsetHeight; // Force reflow

        // Dispatch a resize event to ensure chart resizes properly
        window.dispatchEvent(new Event('resize'));

        console.log('✅ Trading page layout recalculation triggered');
      }
    };

    // Multiple triggers to ensure proper initialization
    const timers = [
      setTimeout(triggerLayoutRecalculation, 100),
      setTimeout(triggerLayoutRecalculation, 300),
      setTimeout(triggerLayoutRecalculation, 600)
    ];

    // Cleanup on unmount
    return () => {
      timers.forEach(timer => clearTimeout(timer));
      document.body.style.margin = '';
      document.body.style.padding = '';
      document.body.style.overflow = '';
      document.documentElement.style.height = '';
      document.body.style.height = '';
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 bg-[#0A0A0A] text-white ${
        isWhopUser ? 'whop-trading-fullscreen' : ''
      }`}
    >
      {/* Debug Panel Toggle (localhost only) - Temporarily disabled for deployment */}
      {isLocalhost && (
        <div className="absolute top-4 left-4 z-50">
          <Button
            onClick={() => console.log('Debug panel temporarily disabled')}
            variant="outline"
            size="sm"
            className="bg-gray-900/80 border-gray-700 text-white hover:bg-gray-800"
          >
            Debug (Disabled)
          </Button>
        </div>
      )}

      {/* Full-screen trading chart with proper sizing */}
      <div className="w-full h-full">
        <TradingChart />
      </div>

      {/* Custom styles for Whop full-screen trading */}
      {isWhopUser && (
        <style>{`
          .whop-trading-fullscreen {
            /* Ensure full-screen experience for Whop users */
            z-index: 1000;
          }

          /* Hide any potential Whop bottom navigation for full-screen trading */
          .whop-trading-fullscreen ~ * {
            display: none !important;
          }
        `}</style>
      )}
    </div>
  );
};

export default Trading;
