import React, { useEffect, useRef, useState } from 'react';
import TradingChart from '@/components/TradingChart/TradingChart';
import WatchlistSidebar from '@/components/WatchlistSidebar/WatchlistSidebar';
import { useWhopUser } from '@/contexts/WhopContext';
import { Button } from '@/components/ui/button';

const Trading: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { isWhopUser } = useWhopUser();
  const [selectedSymbol, setSelectedSymbol] = useState('TSLA');
  const [sidebarWidth, setSidebarWidth] = useState(320); // Default expanded width

  // Ensure the page takes full viewport height and removes any default margins
  useEffect(() => {
    // Set body styles for full-screen trading experience
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.overflow = 'hidden';
    document.documentElement.style.height = '100%';
    document.body.style.height = '100%';

    // Trigger layout recalculation after mount to ensure proper chart display
    // This mimics the behavior that happens when dropdown menus are activated
    const triggerLayoutRecalculation = () => {
      if (containerRef.current) {
        // Force a reflow by reading layout properties
        const container = containerRef.current;
        container.offsetHeight; // Force reflow

        // Dispatch a resize event to ensure chart resizes properly
        window.dispatchEvent(new Event('resize'));

        console.log('✅ Trading page layout recalculation triggered');
      }
    };

    // Multiple triggers to ensure proper initialization
    const timers = [
      setTimeout(triggerLayoutRecalculation, 100),
      setTimeout(triggerLayoutRecalculation, 300),
      setTimeout(triggerLayoutRecalculation, 600)
    ];

    // Cleanup on unmount
    return () => {
      timers.forEach(timer => clearTimeout(timer));
      document.body.style.margin = '';
      document.body.style.padding = '';
      document.body.style.overflow = '';
      document.documentElement.style.height = '';
      document.body.style.height = '';
    };
  }, []);

  const handleSymbolSelect = (symbol: string) => {
    setSelectedSymbol(symbol);
  };

  const handleSidebarToggle = (isCollapsed: boolean) => {
    setSidebarWidth(isCollapsed ? 48 : 320); // 48px for collapsed, 320px for expanded
  };

  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 bg-[#0A0A0A] text-white ${
        isWhopUser ? 'whop-trading-fullscreen' : ''
      }`}
    >
      {/* Watchlist Sidebar */}
      <WatchlistSidebar
        onSymbolSelect={handleSymbolSelect}
        onToggle={handleSidebarToggle}
      />

      {/* Full-screen trading chart with proper sizing - adjusted for sidebar */}
      <div
        className="w-full h-full transition-all duration-300 ease-in-out"
        style={{ paddingLeft: `${sidebarWidth}px` }}
      >
        <TradingChart symbol={selectedSymbol} />
      </div>

      {/* Custom styles for Whop full-screen trading */}
      {isWhopUser && (
        <style>{`
          .whop-trading-fullscreen {
            /* Ensure full-screen experience for Whop users */
            z-index: 1000;
          }

          /* Hide any potential Whop bottom navigation for full-screen trading */
          .whop-trading-fullscreen ~ * {
            display: none !important;
          }
        `}</style>
      )}
    </div>
  );
};

export default Trading;
